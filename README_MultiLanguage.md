# PDF Form Filling with Multi-Language Support

## 概述

`fillPDF.py` 现在支持多语言PDF表单填写，包括亚洲语系（中文、日文、韩文）、阿拉伯语、泰语、西里尔字母语言等22种语言。

## 支持的语言

基于 `suppurt.js` 中的语言列表，支持以下语言：

### 拉丁字母语言
- **en** - English (英语)
- **de** - <PERSON><PERSON><PERSON> (德语)
- **es** - Español (西班牙语)
- **fr** - Français (法语)
- **it** - Italiano (意大利语)
- **pt** - Português (葡萄牙语)
- **nl** - Nederlands (荷兰语)
- **hu** - <PERSON><PERSON><PERSON> (匈牙利语)
- **bs** - <PERSON><PERSON><PERSON> (波斯尼亚语)
- **hr** - <PERSON><PERSON><PERSON><PERSON> (克罗地亚语)
- **ro** - Român<PERSON> (罗马尼亚语)
- **id** - Bahasa Indonesia (印尼语)
- **ms** - <PERSON><PERSON><PERSON> (马来语)
- **vi** - Tiế<PERSON> V<PERSON> (越南语)

### 西里尔字母语言
- **ru** - Русский язык (俄语)
- **bg** - български (保加利亚语)

### 阿拉伯语
- **ar** - العربية (阿拉伯语) - 支持从右到左 (RTL)

### 泰语
- **th** - ไทย (泰语)

### 中日韩 (CJK) 语言
- **zh** - 简体中文
- **zh-tw** - 繁體中文
- **ja** - 日本語 (日语)
- **ko** - 한국어 (韩语)

## 主要功能

### 1. 智能语言检测
- 自动检测文本中的主要语言和文字系统
- 支持混合语言文本
- 基于Unicode范围进行精确识别

### 2. 字符宽度自适应
- 根据不同语言调整字符宽度计算
- CJK字符：1.0倍字体大小（等宽）
- 阿拉伯语：0.65倍字体大小
- 泰语：0.70倍字体大小
- 西里尔字母：0.60倍字体大小
- 拉丁字母：0.55倍字体大小

### 3. Google Noto Sans 字体支持
- 自动下载适合的Noto Sans字体
- 支持不同文字系统的专用字体
- 字体缓存管理

### 4. RTL语言支持
- 阿拉伯语等从右到左语言的特殊处理
- 文本方向和对齐调整

## 安装和使用

### 1. 基本使用
```bash
# 查看帮助信息
python fillPDF.py --help

# 查看支持的语言
python fillPDF.py --languages

# 填写PDF表单
python fillPDF.py input.pdf fields.json output.pdf
```

### 2. 下载字体
```bash
# 下载所有Noto Sans字体
python fillPDF.py input.pdf fields.json output.pdf --download-fonts

# 或使用专用字体下载器
python download_noto_fonts.py

# 下载特定文字系统的字体
python download_noto_fonts.py --script cjk
python download_noto_fonts.py --script arabic
```

### 3. 测试多语言支持
```bash
# 运行多语言测试套件
python test_multilang_support.py
```

## JSON字段格式

支持多语言的JSON字段格式示例：

```json
{
  "fields": [
    {
      "name": "chinese_field",
      "full_name": "chinese_field",
      "value": "你好世界",
      "numberOfCells": 8,
      "field_width": 96.0,
      "letterSpace": 0.1
    },
    {
      "name": "arabic_field", 
      "full_name": "arabic_field",
      "value": "مرحبا بالعالم",
      "numberOfCells": 10,
      "field_width": 120.0,
      "letterSpace": 0.1
    },
    {
      "name": "japanese_field",
      "full_name": "japanese_field", 
      "value": "こんにちは世界",
      "numberOfCells": 12,
      "field_width": 144.0,
      "letterSpace": 0.1
    }
  ]
}
```

## 技术实现

### 语言检测算法
1. **Unicode范围检测**：基于字符的Unicode码点识别文字系统
2. **统计分析**：计算不同文字系统字符的比例
3. **启发式规则**：使用特定模式识别具体语言

### 字符间距计算
```python
# 基于语言的字符宽度计算
char_width = font_size * language_specific_factor

# Comb字段的间距计算
cell_width = field_width / numberOfCells
tc_value = cell_width - char_width
```

### 字体管理
- 字体缓存目录：`fonts_cache/`
- 自动下载缺失的字体
- 支持TTF和OTF格式

## 文件结构

```
├── fillPDF.py                    # 主程序（增强多语言支持）
├── download_noto_fonts.py        # 字体下载器
├── test_multilang_support.py     # 多语言测试套件
├── README_MultiLanguage.md       # 本文档
├── fonts_cache/                  # 字体缓存目录
│   ├── NotoSans-latin.ttf
│   ├── NotoSans-cjk.ttf
│   ├── NotoSans-arabic.ttf
│   └── ...
└── test_multilang_fields.json    # 多语言测试数据
```

## 注意事项

### 1. 字体要求
- PDF表单需要嵌入或支持相应的字体
- 建议使用支持Unicode的字体
- Noto Sans字体提供最佳兼容性

### 2. RTL语言限制
- 当前RTL支持是基础实现
- 复杂的双向文本可能需要额外处理
- 建议测试具体的阿拉伯语PDF表单

### 3. 性能考虑
- 首次使用时会下载字体文件
- 大型CJK字体文件可能较大（10-20MB）
- 字体缓存可以提高后续性能

## 故障排除

### 常见问题

1. **字符显示为方块**
   - 检查PDF是否嵌入了相应字体
   - 尝试下载对应的Noto Sans字体

2. **字符间距不正确**
   - 验证numberOfCells和field_width设置
   - 检查语言检测是否正确

3. **RTL文本方向错误**
   - 确认PDF查看器支持RTL显示
   - 检查字段的文本方向设置

### 调试信息
程序会输出详细的调试信息，包括：
- 语言检测结果
- 字符宽度计算
- 字体选择过程
- 间距调整详情

## 更新日志

### v2.0 - 多语言支持
- 添加22种语言支持
- 实现智能语言检测
- 集成Google Noto Sans字体
- 支持RTL语言
- 优化字符间距计算

### v1.0 - 基础版本
- 基本PDF表单填写
- numberOfCells字段支持
- 英语和基础拉丁字符支持
