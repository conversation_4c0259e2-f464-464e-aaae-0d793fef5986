#!/usr/bin/env python3
"""
Multi-language PDF Form Filling Demo
Demonstrates the multi-language capabilities of fillPDF.py
"""

import json
import sys
from pathlib import Path

# Import from fillPDF.py
try:
    from fillPDF import LanguageDetector, SUPPORTED_LANGUAGES
except ImportError as e:
    print(f"Error importing from fillPDF.py: {e}")
    sys.exit(1)

def demo_language_detection():
    """Demonstrate language detection capabilities"""
    print("🌍 Multi-language Detection Demo")
    print("=" * 50)
    
    # Sample texts in different languages
    sample_texts = {
        "English": "Hello, how are you today?",
        "French": "Bonjour, comment allez-vous?",
        "Spanish": "Hola, ¿cómo estás hoy?",
        "German": "Hallo, wie geht es dir heute?",
        "Italian": "Ciao, come stai oggi?",
        "Portuguese": "Olá, como você está hoje?",
        "Russian": "Привет, как дела сегодня?",
        "Bulgarian": "Здравей, как си днес?",
        "Arabic": "مرحبا، كيف حالك اليوم؟",
        "Thai": "สวัสดี วันนี้เป็นอย่างไรบ้าง?",
        "Chinese": "你好，你今天怎么样？",
        "Japanese": "こんにちは、今日はいかがですか？",
        "Korean": "안녕하세요, 오늘 어떠세요?",
        "Vietnamese": "Xin chào, hôm nay bạn thế nào?",
        "Indonesian": "Halo, apa kabar hari ini?",
    }
    
    detector = LanguageDetector()
    
    for language_name, text in sample_texts.items():
        detected_lang = detector.detect_language(text)
        script = detector.detect_script(text)
        char_width_factor = detector.get_char_width_factor(text)
        is_rtl = detector.is_rtl_text(text)
        
        # Get language info
        lang_info = SUPPORTED_LANGUAGES.get(detected_lang, {})
        lang_display_name = lang_info.get('name', detected_lang)
        
        rtl_indicator = " 🔄" if is_rtl else ""
        
        print(f"{language_name:12} | {text[:30]:30} | {detected_lang:6} | {script:10} | {char_width_factor:.2f}{rtl_indicator}")

def create_comprehensive_test_json():
    """Create a comprehensive test JSON with all supported languages"""
    print("\n📝 Creating Comprehensive Multi-language Test Data")
    print("=" * 50)
    
    # Sample data for each supported language
    language_samples = {
        'en': "Hello World",
        'fr': "Bonjour monde",
        'es': "Hola mundo", 
        'de': "Hallo Welt",
        'it': "Ciao mondo",
        'pt': "Olá mundo",
        'nl': "Hallo wereld",
        'ru': "Привет мир",
        'bg': "Здравей свят",
        'ar': "مرحبا بالعالم",
        'th': "สวัสดีโลก",
        'zh': "你好世界",
        'zh-tw': "你好世界",
        'ja': "こんにちは世界",
        'ko': "안녕하세요 세계",
        'vi': "Xin chào thế giới",
        'id': "Halo dunia",
        'ms': "Hello dunia",
        'hu': "Helló világ",
        'hr': "Pozdrav svijete",
        'bs': "Zdravo svijete",
        'ro': "Salut lume",
    }
    
    fields = []
    
    for i, (lang_code, text) in enumerate(language_samples.items()):
        lang_info = SUPPORTED_LANGUAGES.get(lang_code, {})
        lang_name = lang_info.get('name', lang_code)
        
        # Calculate appropriate field width based on text length and character width
        char_width_factor = lang_info.get('char_width_factor', 0.55)
        text_length = len(text)
        numberOfCells = max(text_length + 2, 8)  # At least 8 cells, or text length + 2
        field_width = numberOfCells * 12.0  # 12 points per cell
        
        field = {
            "name": f"{lang_code}_field",
            "full_name": f"{lang_code}_field",
            "value": text,
            "numberOfCells": numberOfCells,
            "field_width": field_width,
            "letterSpace": 0.1,
            "language": lang_code,
            "language_name": lang_name,
            "char_width_factor": char_width_factor
        }
        
        fields.append(field)
        print(f"  {lang_code:6} | {lang_name:20} | {text:20} | {numberOfCells:2} cells | {field_width:6.1f}pt")
    
    test_data = {
        "description": "Comprehensive multi-language test data for fillPDF.py",
        "total_languages": len(fields),
        "supported_scripts": list(set(lang['script'] for lang in SUPPORTED_LANGUAGES.values())),
        "fields": fields
    }
    
    output_file = "comprehensive_multilang_test.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Created comprehensive test file: {output_file}")
    print(f"   Total fields: {len(fields)}")
    print(f"   Total languages: {len(language_samples)}")
    
    return output_file

def demo_character_width_calculation():
    """Demonstrate character width calculation for different scripts"""
    print("\n📏 Character Width Calculation Demo")
    print("=" * 50)
    
    from fillPDF import PDFFormFiller
    
    # Create a dummy filler for testing
    filler = PDFFormFiller("dummy.pdf")
    
    test_cases = [
        ("Latin", "Hello World", 12.0),
        ("French", "Bonjour monde", 12.0),
        ("Cyrillic", "Привет мир", 12.0),
        ("Arabic", "مرحبا بالعالم", 12.0),
        ("Thai", "สวัสดีโลก", 12.0),
        ("Chinese", "你好世界", 12.0),
        ("Japanese", "こんにちは世界", 12.0),
        ("Korean", "안녕하세요 세계", 12.0),
    ]
    
    print(f"{'Script':<12} | {'Text':<15} | {'Font Size':<10} | {'Char Width':<12} | {'Factor':<8}")
    print("-" * 70)
    
    for script_name, text, font_size in test_cases:
        char_width = filler._calculate_character_width(font_size, text)
        lang_code = filler.language_detector.detect_language(text)
        factor = SUPPORTED_LANGUAGES.get(lang_code, {}).get('char_width_factor', 0.55)
        
        print(f"{script_name:<12} | {text:<15} | {font_size:<10.1f} | {char_width:<12.2f} | {factor:<8.2f}")

def show_usage_examples():
    """Show usage examples for the multi-language PDF filler"""
    print("\n💡 Usage Examples")
    print("=" * 50)
    
    examples = [
        {
            "description": "Show supported languages",
            "command": "python fillPDF.py --languages"
        },
        {
            "description": "Download Google Noto Sans fonts",
            "command": "python fillPDF.py dummy.pdf dummy.json output.pdf --download-fonts"
        },
        {
            "description": "Fill PDF with multi-language content",
            "command": "python fillPDF.py form.pdf comprehensive_multilang_test.json output_multilang.pdf"
        },
        {
            "description": "Test specific script font download",
            "command": "python download_noto_fonts.py --script cjk"
        },
        {
            "description": "Verify downloaded fonts",
            "command": "python download_noto_fonts.py --verify"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
        print(f"   {example['command']}")
        print()

def main():
    """Main demo function"""
    print("🌐 Multi-language PDF Form Filling Demo")
    print("Supporting 22 languages across 5 script systems")
    print("=" * 60)
    
    try:
        # Run demonstrations
        demo_language_detection()
        demo_character_width_calculation()
        
        # Create comprehensive test data
        test_file = create_comprehensive_test_json()
        
        # Show usage examples
        show_usage_examples()
        
        print("=" * 60)
        print("✅ Demo completed successfully!")
        print(f"📁 Test file created: {test_file}")
        print("\n🚀 Next steps:")
        print("1. Run: python fillPDF.py --languages")
        print("2. Create or use a PDF form with the field names from the test JSON")
        print(f"3. Run: python fillPDF.py your_form.pdf {test_file} output.pdf")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
