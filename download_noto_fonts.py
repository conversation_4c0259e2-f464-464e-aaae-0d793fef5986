#!/usr/bin/env python3
"""
Google Noto Sans Font Downloader
Downloads Noto Sans fonts for multi-language PDF support
"""

import os
import sys
import urllib.request
import urllib.parse
import zipfile
import shutil
from pathlib import Path
from typing import Dict, Optional

# Google Noto Sans font download URLs
NOTO_FONT_DOWNLOADS = {
    'latin': {
        'url': 'https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Regular.ttf',
        'filename': 'NotoSans-Regular.ttf'
    },
    'cyrillic': {
        'url': 'https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Regular.ttf',
        'filename': 'NotoSans-Regular.ttf'
    },
    'arabic': {
        'url': 'https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansArabic/NotoSansArabic-Regular.ttf',
        'filename': 'NotoSansArabic-Regular.ttf'
    },
    'thai': {
        'url': 'https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansThai/NotoSansThai-Regular.ttf',
        'filename': 'NotoSansThai-Regular.ttf'
    },
    'cjk': {
        'url': 'https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf',
        'filename': 'NotoSansCJKsc-Regular.otf'
    },
    'japanese': {
        'url': 'https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/Japanese/NotoSansCJKjp-Regular.otf',
        'filename': 'NotoSansCJKjp-Regular.otf'
    },
    'korean': {
        'url': 'https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/Korean/NotoSansCJKkr-Regular.otf',
        'filename': 'NotoSansCJKkr-Regular.otf'
    }
}

class NotoFontDownloader:
    def __init__(self, cache_dir: str = "fonts_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
    def download_font(self, script: str, force_download: bool = False) -> bool:
        """Download a specific Noto Sans font"""
        if script not in NOTO_FONT_DOWNLOADS:
            print(f"Error: No download URL available for script '{script}'")
            return False
        
        font_info = NOTO_FONT_DOWNLOADS[script]
        font_path = self.cache_dir / f"NotoSans-{script}.ttf"
        
        # Check if font already exists
        if font_path.exists() and not force_download:
            print(f"Font for {script} already exists: {font_path}")
            return True
        
        try:
            print(f"Downloading Noto Sans font for {script}...")
            print(f"URL: {font_info['url']}")
            
            # Download the font file
            urllib.request.urlretrieve(font_info['url'], font_path)
            
            # Verify the download
            if font_path.exists() and font_path.stat().st_size > 0:
                print(f"✓ Successfully downloaded: {font_path}")
                print(f"  File size: {font_path.stat().st_size / 1024:.1f} KB")
                return True
            else:
                print(f"✗ Download failed or file is empty: {font_path}")
                return False
                
        except Exception as e:
            print(f"✗ Error downloading font for {script}: {e}")
            if font_path.exists():
                font_path.unlink()  # Remove incomplete file
            return False
    
    def download_all_fonts(self, force_download: bool = False) -> Dict[str, bool]:
        """Download all available Noto Sans fonts"""
        results = {}
        
        print(f"Downloading Noto Sans fonts to: {self.cache_dir}")
        print("=" * 60)
        
        for script in NOTO_FONT_DOWNLOADS.keys():
            results[script] = self.download_font(script, force_download)
            print()  # Add spacing between downloads
        
        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        print("=" * 60)
        print(f"Download Summary: {successful}/{total} fonts downloaded successfully")
        
        if successful < total:
            print("\nFailed downloads:")
            for script, success in results.items():
                if not success:
                    print(f"  ✗ {script}")
        
        return results
    
    def list_downloaded_fonts(self):
        """List all downloaded fonts"""
        print(f"Fonts in cache directory: {self.cache_dir}")
        print("-" * 40)
        
        font_files = list(self.cache_dir.glob("*.ttf")) + list(self.cache_dir.glob("*.otf"))
        
        if not font_files:
            print("No fonts found in cache directory")
            return
        
        for font_file in sorted(font_files):
            size_kb = font_file.stat().st_size / 1024
            print(f"  {font_file.name:<30} ({size_kb:.1f} KB)")
    
    def verify_fonts(self):
        """Verify that downloaded fonts are valid"""
        print("Verifying downloaded fonts...")
        print("-" * 40)
        
        for script in NOTO_FONT_DOWNLOADS.keys():
            font_path = self.cache_dir / f"NotoSans-{script}.ttf"
            
            if font_path.exists():
                size = font_path.stat().st_size
                if size > 1000:  # At least 1KB
                    print(f"  ✓ {script:<10} - {size/1024:.1f} KB")
                else:
                    print(f"  ✗ {script:<10} - File too small ({size} bytes)")
            else:
                print(f"  ✗ {script:<10} - Not found")

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("Google Noto Sans Font Downloader")
        print("Usage: python download_noto_fonts.py [options]")
        print("\nOptions:")
        print("  --help, -h       Show this help message")
        print("  --list, -l       List downloaded fonts")
        print("  --verify, -v     Verify downloaded fonts")
        print("  --force, -f      Force re-download existing fonts")
        print("  --script SCRIPT  Download specific script only")
        print("\nAvailable scripts:")
        for script in NOTO_FONT_DOWNLOADS.keys():
            print(f"  {script}")
        return
    
    downloader = NotoFontDownloader()
    
    # Parse command line arguments
    if '--list' in sys.argv or '-l' in sys.argv:
        downloader.list_downloaded_fonts()
        return
    
    if '--verify' in sys.argv or '-v' in sys.argv:
        downloader.verify_fonts()
        return
    
    force_download = '--force' in sys.argv or '-f' in sys.argv
    
    # Check for specific script download
    if '--script' in sys.argv:
        try:
            script_index = sys.argv.index('--script') + 1
            if script_index < len(sys.argv):
                script = sys.argv[script_index]
                if script in NOTO_FONT_DOWNLOADS:
                    downloader.download_font(script, force_download)
                else:
                    print(f"Error: Unknown script '{script}'")
                    print(f"Available scripts: {', '.join(NOTO_FONT_DOWNLOADS.keys())}")
            else:
                print("Error: --script requires a script name")
        except ValueError:
            print("Error: --script requires a script name")
        return
    
    # Download all fonts
    downloader.download_all_fonts(force_download)

if __name__ == "__main__":
    main()
