#!/usr/bin/env python3
"""
Test script for multi-language support in fillPDF.py
Tests language detection, character width calculation, and font management
"""

import sys
import json
from pathlib import Path

# Import the classes from fillPDF.py
try:
    from fillPDF import (
        LanguageDetector, FontManager, SUPPORTED_LANGUAGES, 
        UNICODE_RANGES, PDFFormFiller
    )
except ImportError as e:
    print(f"Error importing from fillPDF.py: {e}")
    sys.exit(1)

def test_language_detection():
    """Test language detection functionality"""
    print("=" * 60)
    print("TESTING LANGUAGE DETECTION")
    print("=" * 60)
    
    test_texts = [
        ("Hello World", "en"),
        ("Bonjour le monde", "fr"),
        ("Hola mundo", "es"),
        ("Hallo Welt", "de"),
        ("Ciao mondo", "it"),
        ("Olá mundo", "pt"),
        ("Привет мир", "ru"),
        ("Здравей свят", "bg"),
        ("مرحبا بالعالم", "ar"),
        ("สวัสดีชาวโลก", "th"),
        ("こんにちは世界", "ja"),
        ("안녕하세요 세계", "ko"),
        ("你好世界", "zh"),
        ("你好世界", "zh-tw"),
        ("Xin chào thế giới", "vi"),
        ("Halo dunia", "id"),
        ("Hej världen", "sv"),
        ("Hei maailma", "fi"),
    ]
    
    detector = LanguageDetector()
    
    for text, expected_lang in test_texts:
        detected_lang = detector.detect_language(text)
        script = detector.detect_script(text)
        char_width_factor = detector.get_char_width_factor(text)
        is_rtl = detector.is_rtl_text(text)
        
        status = "✓" if detected_lang == expected_lang else "✗"
        rtl_indicator = " (RTL)" if is_rtl else ""
        
        print(f"{status} '{text[:20]:<20}' -> {detected_lang:6} | {script:10} | {char_width_factor:.2f}{rtl_indicator}")

def test_character_width_calculation():
    """Test character width calculation for different languages"""
    print("\n" + "=" * 60)
    print("TESTING CHARACTER WIDTH CALCULATION")
    print("=" * 60)
    
    # Create a dummy PDF filler for testing
    filler = PDFFormFiller("dummy.pdf")
    
    test_cases = [
        ("ABC123", 12.0),  # English
        ("Français", 12.0),  # French
        ("Русский", 12.0),  # Russian
        ("العربية", 12.0),  # Arabic
        ("ไทย", 12.0),  # Thai
        ("日本語", 12.0),  # Japanese
        ("한국어", 12.0),  # Korean
        ("中文", 12.0),  # Chinese
    ]
    
    print(f"{'Text':<15} | {'Font Size':<10} | {'Char Width':<12} | {'Language':<8} | {'Script'}")
    print("-" * 70)
    
    for text, font_size in test_cases:
        char_width = filler._calculate_character_width(font_size, text)
        lang = filler.language_detector.detect_language(text)
        script = filler.language_detector.detect_script(text)
        
        print(f"{text:<15} | {font_size:<10.1f} | {char_width:<12.2f} | {lang:<8} | {script}")

def test_font_management():
    """Test font management functionality"""
    print("\n" + "=" * 60)
    print("TESTING FONT MANAGEMENT")
    print("=" * 60)
    
    font_manager = FontManager("test_fonts_cache")
    
    # Test font path retrieval for different scripts
    scripts = ['latin', 'cyrillic', 'arabic', 'thai', 'cjk']
    
    for script in scripts:
        font_path = font_manager.get_font_path(script)
        if font_path:
            print(f"✓ {script:<10} -> {font_path}")
        else:
            print(f"✗ {script:<10} -> No font available")

def create_test_json():
    """Create a test JSON file with multi-language content"""
    print("\n" + "=" * 60)
    print("CREATING TEST JSON WITH MULTI-LANGUAGE CONTENT")
    print("=" * 60)
    
    test_data = {
        "fields": [
            {
                "name": "english_field",
                "full_name": "english_field",
                "value": "Hello World",
                "numberOfCells": 10,
                "field_width": 120.0,
                "letterSpace": 0.1
            },
            {
                "name": "chinese_field",
                "full_name": "chinese_field", 
                "value": "你好世界",
                "numberOfCells": 8,
                "field_width": 96.0,
                "letterSpace": 0.1
            },
            {
                "name": "arabic_field",
                "full_name": "arabic_field",
                "value": "مرحبا",
                "numberOfCells": 6,
                "field_width": 72.0,
                "letterSpace": 0.1
            },
            {
                "name": "japanese_field",
                "full_name": "japanese_field",
                "value": "こんにちは",
                "numberOfCells": 10,
                "field_width": 120.0,
                "letterSpace": 0.1
            },
            {
                "name": "thai_field",
                "full_name": "thai_field",
                "value": "สวัสดี",
                "numberOfCells": 8,
                "field_width": 96.0,
                "letterSpace": 0.1
            },
            {
                "name": "russian_field",
                "full_name": "russian_field",
                "value": "Привет",
                "numberOfCells": 8,
                "field_width": 96.0,
                "letterSpace": 0.1
            }
        ]
    }
    
    output_file = "test_multilang_fields.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✓ Created test JSON file: {output_file}")
    print(f"  Contains {len(test_data['fields'])} fields with different languages")
    
    return output_file

def test_language_support_info():
    """Test the language support information display"""
    print("\n" + "=" * 60)
    print("TESTING LANGUAGE SUPPORT INFO DISPLAY")
    print("=" * 60)
    
    filler = PDFFormFiller("dummy.pdf")
    filler.print_language_support_info()

def main():
    """Run all tests"""
    print("Multi-language Support Test Suite")
    print("Testing fillPDF.py multi-language functionality")
    
    try:
        # Run all tests
        test_language_detection()
        test_character_width_calculation()
        test_font_management()
        test_language_support_info()
        
        # Create test data
        test_json_file = create_test_json()
        
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print("✓ Language detection tests completed")
        print("✓ Character width calculation tests completed")
        print("✓ Font management tests completed")
        print("✓ Language support info tests completed")
        print(f"✓ Test JSON file created: {test_json_file}")
        
        print(f"\nTo test with actual PDF filling, run:")
        print(f"python fillPDF.py your_pdf.pdf {test_json_file} output_multilang.pdf")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
